const { env } = require('process');

// Determine target based on environment
const target = env.DOCKER_ENV ? 'http://backend:80' :
  env.ASPNETCORE_HTTPS_PORT ? `https://localhost:${env.ASPNETCORE_HTTPS_PORT}` :
  env.ASPNETCORE_URLS ? env.ASPNETCORE_URLS.split(';')[0] : 'https://localhost:7115';

const PROXY_CONFIG = [
  {
    context: [
      "/api/**",
      "/swagger/**",
      "/weatherforecast"
    ],
    target,
    secure: false,
    changeOrigin: true,
    logLevel: "debug"
  }
]

module.exports = PROXY_CONFIG;
