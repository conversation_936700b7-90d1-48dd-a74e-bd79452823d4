# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
*.md

# Docker
Dockerfile*
docker-compose*
.dockerignore

# IDE
.vs
.vscode
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Build outputs
dist/
build/
bin/
obj/

# Visual Studio
.vs/
*.user
*.suo
*.userosscache
*.sln.docstates

# Test results
TestResults/
[Tt]est[Rr]esult*/
*.trx

# NuGet
packages/
*.nupkg
project.lock.json
project.fragment.lock.json
artifacts/
