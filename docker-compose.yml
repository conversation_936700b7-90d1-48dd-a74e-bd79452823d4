services:
  # SQL Server Database
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: trackbus-sqlserver
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=TrackBus123!
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
    networks:
      - trackbus-network
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P TrackBus123! -Q 'SELECT 1'"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # .NET Backend API
  backend:
    build:
      context: .
      dockerfile: TrackBus.Server/Dockerfile
    container_name: trackbus-backend
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver,1433;Database=TrackBusDb;User Id=sa;Password=TrackBus123!;MultipleActiveResultSets=true;TrustServerCertificate=true;Encrypt=false
    ports:
      - "5000:80"
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - trackbus-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Angular Frontend
  frontend:
    build:
      context: .
      dockerfile: trackbus.client/Dockerfile
    container_name: trackbus-frontend
    ports:
      - "4200:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - trackbus-network
    restart: unless-stopped

volumes:
  sqlserver_data:
    driver: local

networks:
  trackbus-network:
    driver: bridge
