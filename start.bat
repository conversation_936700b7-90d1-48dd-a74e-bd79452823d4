@echo off
echo 🚀 Starting TrackBus Application...
echo.

REM Check if <PERSON><PERSON> is running
docker info >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not running. Please start Docker and try again.
    pause
    exit /b 1
)

REM Check if Docker Compose is available
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose is not installed. Please install Docker Compose and try again.
    pause
    exit /b 1
)

echo ✅ Docker is running
echo ✅ Docker Compose is available
echo.

REM Stop any existing containers
echo 🛑 Stopping any existing containers...
docker-compose down

echo.
echo 🔨 Building and starting containers...
echo This may take a few minutes on the first run...
echo.

REM Build and start the containers
docker-compose up --build -d

echo.
echo ⏳ Waiting for services to be ready...

REM Wait for backend to be healthy
echo Waiting for backend to be ready...
set /a timeout=300
set /a counter=0

:wait_backend
if %counter% geq %timeout% (
    echo ❌ Backend failed to start within %timeout% seconds
    echo Check logs with: docker-compose logs backend
    pause
    exit /b 1
)

curl -f http://localhost:5000/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Backend is ready!
    goto wait_frontend
)

timeout /t 5 /nobreak >nul
set /a counter+=5
echo Still waiting for backend... (%counter%/%timeout% seconds)
goto wait_backend

:wait_frontend
REM Wait for frontend to be ready
echo Waiting for frontend to be ready...
set /a counter=0

:wait_frontend_loop
if %counter% geq 60 (
    echo ❌ Frontend failed to start within 60 seconds
    echo Check logs with: docker-compose logs frontend
    pause
    exit /b 1
)

curl -f http://localhost:4200 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Frontend is ready!
    goto success
)

timeout /t 2 /nobreak >nul
set /a counter+=2
echo Still waiting for frontend... (%counter%/60 seconds)
goto wait_frontend_loop

:success
echo.
echo 🎉 TrackBus is now running!
echo.
echo 📱 Frontend: http://localhost:4200
echo 🔧 Backend API: http://localhost:5000
echo 📚 API Documentation: http://localhost:5000/swagger
echo ❤️  Health Check: http://localhost:5000/health
echo.
echo To stop the application, run: docker-compose down
echo To view logs, run: docker-compose logs -f
echo.
pause
