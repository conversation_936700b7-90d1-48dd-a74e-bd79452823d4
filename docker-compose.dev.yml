services:
  # SQL Server Database (same as production)
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: trackbus-sqlserver-dev
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=TrackBus123!
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_dev_data:/var/opt/mssql
    networks:
      - trackbus-network
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P TrackBus123! -Q 'SELECT 1'"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # .NET Backend API with hot reload
  backend:
    build:
      context: .
      dockerfile: TrackBus.Server/Dockerfile.dev
    container_name: trackbus-backend-dev
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver,1433;Database=TrackBusDb;User Id=sa;Password=TrackBus123!;MultipleActiveResultSets=true;TrustServerCertificate=true;Encrypt=false
    ports:
      - "5000:80"
    volumes:
      - ./TrackBus.Server:/app
      - /app/bin
      - /app/obj
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - trackbus-network
    restart: unless-stopped

  # Angular Frontend with hot reload
  frontend:
    build:
      context: .
      dockerfile: trackbus.client/Dockerfile.dev
    container_name: trackbus-frontend-dev
    ports:
      - "4200:4200"
    volumes:
      - ./trackbus.client:/app
      - /app/node_modules
    environment:
      - CHOKIDAR_USEPOLLING=true
    depends_on:
      - backend
    networks:
      - trackbus-network
    restart: unless-stopped

volumes:
  sqlserver_dev_data:
    driver: local

networks:
  trackbus-network:
    driver: bridge
