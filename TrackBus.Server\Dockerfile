# Use the official .NET 8 SDK image for building
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy csproj and restore dependencies
COPY TrackBus.Server/TrackBus.Server.csproj TrackBus.Server/
RUN dotnet restore TrackBus.Server/TrackBus.Server.csproj

# Copy the rest of the source code
COPY TrackBus.Server/ TrackBus.Server/

# Build the application
WORKDIR /src/TrackBus.Server
RUN dotnet build TrackBus.Server.csproj -c Release -o /app/build

# Publish the application
RUN dotnet publish TrackBus.Server.csproj -c Release -o /app/publish

# Use the official .NET 8 SDK image for the final stage (needed for EF tools)
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS runtime
WORKDIR /app

# Install SQL Server tools for health checks and migrations
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Entity Framework tools
RUN dotnet tool install --global dotnet-ef
ENV PATH="$PATH:/root/.dotnet/tools"

# Copy the published application
COPY --from=build /app/publish .

# Copy the entrypoint script
COPY TrackBus.Server/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# Expose port 80
EXPOSE 80

# Set the entry point
ENTRYPOINT ["/entrypoint.sh"]
