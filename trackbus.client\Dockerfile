# Stage 1: Build the Angular application
FROM node:18-alpine AS build

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY trackbus.client/package*.json ./

# Install dependencies (including dev dependencies for build)
RUN npm ci

# Copy the rest of the application code
COPY trackbus.client/ .

# Build the Angular application for production
RUN npm run build

# Stage 2: Serve the application with nginx
FROM nginx:alpine AS runtime

# Copy the built Angular app to nginx html directory
COPY --from=build /app/dist/trackbus.client /usr/share/nginx/html

# Copy custom nginx configuration
COPY trackbus.client/nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
