# TrackBus - Journey Management System

A comprehensive bus journey management system for Trasporti Urbani Globali (TUG).

## 🚀 Quick Start with Docker

### Prerequisites
- [Docker](https://www.docker.com/get-started) installed on your machine
- [Docker Compose](https://docs.docker.com/compose/install/) (usually included with Docker Desktop)

### Running the Application

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd mahmuti-adrijan
   ```

2. **Start the application**

   **Option A: Using the startup script (Recommended)**
   ```bash
   # On Linux/macOS
   chmod +x start.sh
   ./start.sh

   # On Windows
   start.bat
   ```

   **Option B: Using Docker Compose directly**
   ```bash
   docker-compose up --build
   ```

3. **Access the application**
   - **Frontend**: http://localhost:4200
   - **Backend API**: http://localhost:5000
   - **Swagger Documentation**: http://localhost:5000/swagger
   - **Health Check**: http://localhost:5000/health

4. **Stop the application**
   ```bash
   docker-compose down
   ```

### Database Management

The application uses SQL Server running in a Docker container. The database is automatically created and seeded with initial data when the application starts.

To reset the database:
```bash
docker-compose down -v  # This removes the database volume
docker-compose up --build
```

## 🛠️ Tech Stack

- **Frontend Framework**: Angular 18
- **Backend**: .NET 8 Web API
- **Database**: Microsoft SQL Server 2022
- **Containerization**: Docker & Docker Compose

## Project Purpose

The purpose of this project is to create a user-friendly web interface and a RESTFul API for TUG's bus company operations. This application will consume the TrackBus RESTful APIs to enable the management of journeys and stops.

## System entities explanation

As written above, the entities will be:

- Journey: Represents the bus journeys. Every journey is composed of a unique code, description and a sequence of ordered stops.
- Stop: Represents the stop point where the bus has to stop to pick up and drop off passengers. Every stop is composed of a unique code, description and spatial coordinates (X,Y). Each stop can be used by multiple journeys.

## Frontend Features

### Journey Management

- **Create Journey**: Provide a form to input a unique journey code, description, and select ordered stops.
- **List of Journeys**: Display all journeys in a searchable and sortable table.
- **Journey Details**: Show detailed information about a specific journey, including its stops.
- **Edit Journey**: Allow editing of a journey’s code, description, or stops.
- **Delete Journey**: Enable deletion of a journey with confirmation.

### Stops Management

- **Create Stop**: Provide a form to input unique stop code, description, and spatial coordinates (X, Y).
- **List of Stops**: Display all stops in a searchable and sortable table.
- **Stop Details**: Show detailed information about a specific stop.
- **Edit Stop**: Allow editing of a stop’s code, description, or coordinates.
- **Delete Stop**: Enable deletion of a stop with confirmation. If the stop is used by Journey it can't be deleted.

## RESTful API

The API will implement the CRUD operations (Create, Read, Update, Delete) for the entities: Journey and Stop.

Journey API features:

- Create Journey.
- List of all Journeys.
- Journey details.
- Edit Journey.
- Delete Journey.

Stops API features:

- Create Stop.
- List of all Stops.
- Stop details.
- Edit Stop.
- Delete Stop.

## Optional functionalities

### Additional Feature

- **Passing times**: manage passing times of stops: different journeys can have the same stop with different passing times. Passing times can be represented in any convenient form (Backend and Fronted), passing times must be ordered temporally and user must be notified if a modification renders the Journey invalid/inconsistent.

### Frontend

The frontend will be a single-page application (SPA) interacting with the TrackBus RESTful APIs. It will:

- **Consume APIs**: Use endpoints provided by the backend for CRUD operations on Journeys and Stops.

#### Optional

- **Mapping Integration**: Use a mapping library to display stop locations.
- **Responsive Design**: Ensure the UI is optimized for both desktop and mobile devices.

### Backend

The system components will be a .NET 8 RESTful API and an instance of MS Sql Server to manage data persistence.

#### Optional

- **API Authentication**: Include a login mechanism to restrict access to authorized users.
- **Server side pagination**: Implement server-side pagination.

## 🔧 Development Setup

### Local Development (without Docker)

If you prefer to run the application locally without Docker:

#### Prerequisites
- [.NET 8 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
- [Node.js 18+](https://nodejs.org/)
- [SQL Server](https://www.microsoft.com/en-us/sql-server/sql-server-downloads) or SQL Server Express

#### Backend Setup
1. Navigate to the server directory:
   ```bash
   cd TrackBus.Server
   ```

2. Update the connection string in `appsettings.json` to point to your local SQL Server instance

3. Run Entity Framework migrations:
   ```bash
   dotnet ef database update
   ```

4. Start the backend:
   ```bash
   dotnet run
   ```

#### Frontend Setup
1. Navigate to the client directory:
   ```bash
   cd trackbus.client
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm start
   ```

### Docker Development

For development with Docker, you can use the development compose file:
```bash
docker-compose -f docker-compose.dev.yml up --build
```

This enables hot reloading for both frontend and backend.

## 🐛 Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Check what's using the port
   netstat -ano | findstr :5000
   # Kill the process or change the port in docker-compose.yml
   ```

2. **Database connection issues**
   - Ensure SQL Server container is running: `docker-compose ps`
   - Check logs: `docker-compose logs sqlserver`
   - Wait for SQL Server to fully initialize (can take 30-60 seconds on first run)

3. **Frontend not loading**
   - Check if Angular container is running: `docker-compose ps`
   - Check logs: `docker-compose logs frontend`
   - Clear browser cache and try again

4. **Build failures**
   ```bash
   # Clean Docker cache
   docker system prune -a
   # Rebuild without cache
   docker-compose build --no-cache
   ```

### Logs and Debugging

View logs for specific services:
```bash
# All services
docker-compose logs

# Specific service
docker-compose logs backend
docker-compose logs frontend
docker-compose logs sqlserver

# Follow logs in real-time
docker-compose logs -f backend
```

## 📁 Project Structure

```
mahmuti-adrijan/
├── TrackBus.Server/          # .NET 8 Web API
│   ├── Controllers/          # API Controllers
│   ├── Data/                 # Database Context & Migrations
│   ├── Models/               # Entity Models
│   ├── Services/             # Business Logic
│   ├── Repositories/         # Data Access Layer
│   └── DTOs/                 # Data Transfer Objects
├── trackbus.client/          # Angular 18 Frontend
│   ├── src/                  # Source code
│   ├── public/               # Static assets
│   └── package.json          # Node.js dependencies
├── docker-compose.yml        # Production Docker setup
├── docker-compose.dev.yml    # Development Docker setup
├── start.sh                  # Linux/macOS startup script
├── start.bat                 # Windows startup script
└── README.md                 # This file
```
