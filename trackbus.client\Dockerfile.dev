# Development Dockerfile for Angular Frontend with hot reload
FROM node:18-alpine
WORKDIR /app

# Install Angular CLI globally
RUN npm install -g @angular/cli

# Copy package.json and package-lock.json
COPY trackbus.client/package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application code
COPY trackbus.client/ .

# Expose port 4200
EXPOSE 4200

# Start the development server with hot reload
CMD ["ng", "serve", "--host", "0.0.0.0", "--port", "4200", "--poll", "2000"]
