#!/bin/bash

echo "🚀 Starting TrackBus Application..."
echo ""

# Check if <PERSON><PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose and try again."
    exit 1
fi

echo "✅ Docker is running"
echo "✅ Docker Compose is available"
echo ""

# Stop any existing containers
echo "🛑 Stopping any existing containers..."
docker-compose down

echo ""
echo "🔨 Building and starting containers..."
echo "This may take a few minutes on the first run..."
echo ""

# Build and start the containers
docker-compose up --build -d

echo ""
echo "⏳ Waiting for services to be ready..."

# Wait for backend to be healthy
echo "Waiting for backend to be ready..."
timeout=300  # 5 minutes
counter=0
while [ $counter -lt $timeout ]; do
    if curl -f http://localhost:5000/health > /dev/null 2>&1; then
        echo "✅ Backend is ready!"
        break
    fi
    sleep 5
    counter=$((counter + 5))
    echo "Still waiting for backend... ($counter/$timeout seconds)"
done

if [ $counter -ge $timeout ]; then
    echo "❌ Backend failed to start within $timeout seconds"
    echo "Check logs with: docker-compose logs backend"
    exit 1
fi

# Wait for frontend to be ready
echo "Waiting for frontend to be ready..."
counter=0
while [ $counter -lt 60 ]; do
    if curl -f http://localhost:4200 > /dev/null 2>&1; then
        echo "✅ Frontend is ready!"
        break
    fi
    sleep 2
    counter=$((counter + 2))
    echo "Still waiting for frontend... ($counter/60 seconds)"
done

if [ $counter -ge 60 ]; then
    echo "❌ Frontend failed to start within 60 seconds"
    echo "Check logs with: docker-compose logs frontend"
    exit 1
fi

echo ""
echo "🎉 TrackBus is now running!"
echo ""
echo "📱 Frontend: http://localhost:4200"
echo "🔧 Backend API: http://localhost:5000"
echo "📚 API Documentation: http://localhost:5000/swagger"
echo "❤️  Health Check: http://localhost:5000/health"
echo ""
echo "To stop the application, run: docker-compose down"
echo "To view logs, run: docker-compose logs -f"
echo ""
