#!/bin/bash
set -e

echo "Starting TrackBus Server..."

# Wait for SQL Server to be ready
echo "Waiting for SQL Server to be ready..."
until /opt/mssql-tools/bin/sqlcmd -S sqlserver -U sa -P TrackBus123! -Q "SELECT 1" > /dev/null 2>&1; do
    echo "SQL Server is unavailable - sleeping"
    sleep 5
done

echo "SQL Server is ready!"

# Run Entity Framework migrations
echo "Running database migrations..."
dotnet ef database update --no-build --verbose

# Start the application
echo "Starting the application..."
exec dotnet TrackBus.Server.dll
