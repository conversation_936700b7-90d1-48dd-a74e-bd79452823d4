# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile*
.dockerignore

# Environment
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
*.log

# Coverage
coverage/
.nyc_output/

# Angular
.angular/
e2e/

# Test
karma.conf.js
*.spec.ts
