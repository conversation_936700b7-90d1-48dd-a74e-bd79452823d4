# Development Dockerfile for .NET Backend with hot reload
FROM mcr.microsoft.com/dotnet/sdk:8.0
WORKDIR /app

# Install dotnet-ef tool for migrations
RUN dotnet tool install --global dotnet-ef
ENV PATH="$PATH:/root/.dotnet/tools"

# Copy csproj and restore dependencies
COPY TrackBus.Server/TrackBus.Server.csproj .
RUN dotnet restore

# Copy the rest of the source code
COPY TrackBus.Server/ .

# Expose port 80
EXPOSE 80

# Use dotnet watch for hot reload
CMD ["dotnet", "watch", "run", "--urls", "http://0.0.0.0:80"]
